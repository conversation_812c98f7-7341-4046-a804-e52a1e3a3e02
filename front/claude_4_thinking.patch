diff --git a/core/src/providers/anthropic.rs b/core/src/providers/anthropic.rs
index fdf569dcf..9a39a4d18 100644
--- a/core/src/providers/anthropic.rs
+++ b/core/src/providers/anthropic.rs
@@ -1960,18 +1960,14 @@ impl LLM for AnthropicLLM {
 
         let thinking = match &extras {
             None => None,
-            // We don't pass the thinking parameters in tool use.
-            Some(v) => match tool_choice.is_some() {
-                true => None,
-                false => match v.get("anthropic_beta_thinking") {
-                    Some(Value::Object(s)) => match (s.get("type"), s.get("budget_tokens")) {
-                        (Some(Value::String(t)), Some(Value::Number(b))) => {
-                            Some((t.clone(), b.as_u64().unwrap_or(1024)))
-                        }
-                        _ => None,
-                    },
+            Some(v) => match v.get("anthropic_beta_thinking") {
+                Some(Value::Object(s)) => match (s.get("type"), s.get("budget_tokens")) {
+                    (Some(Value::String(t)), Some(Value::Number(b))) => {
+                        Some((t.clone(), b.as_u64().unwrap_or(1024)))
+                    }
                     _ => None,
                 },
+                _ => None,
             },
         };
 
diff --git a/front/lib/api/assistant/agent.ts b/front/lib/api/assistant/agent.ts
index a72ba45ef..209602b43 100644
--- a/front/lib/api/assistant/agent.ts
+++ b/front/lib/api/assistant/agent.ts
@@ -68,6 +68,7 @@ import type {
   UserMessageType,
   WorkspaceType,
 } from "@app/types";
+import { CLAUDE_4_SONNET_20250514_MODEL_ID } from "@app/types";
 import { assertNever, removeNulls, SUPPORTED_MODEL_CONFIGS } from "@app/types";
 
 const CANCELLATION_CHECK_INTERVAL = 500;
@@ -569,6 +570,14 @@ async function* runMultiActionsAgent(
       agentConfiguration.model.responseFormat
     );
   }
+  if (model.modelId === CLAUDE_4_SONNET_20250514_MODEL_ID) {
+    // Pass some extra field: https://docs.anthropic.com/en/docs/about-claude/models/extended-thinking-models#extended-output-capabilities-beta
+    runConfig.MODEL.anthropic_beta_thinking = {
+      type: "enabled",
+      budget_tokens: 6400,
+    };
+  }
+
   const anthropicBetaFlags = config.getMultiActionsAgentAnthropicBetaFlags();
   if (anthropicBetaFlags) {
     runConfig.MODEL.anthropic_beta_flags = anthropicBetaFlags;
